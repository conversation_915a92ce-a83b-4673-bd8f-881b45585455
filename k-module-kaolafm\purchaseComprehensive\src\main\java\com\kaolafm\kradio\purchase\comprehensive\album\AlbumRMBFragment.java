package com.kaolafm.kradio.purchase.comprehensive.album;

import android.graphics.Paint;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.TypedValue;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.comprehensive.base.PayResDialog;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.purchase.album.AlbumRMBPresenter;
import com.kaolafm.kradio.purchase.album.IAlbumRMBView;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.util.MoneyUtils;
import com.kaolafm.kradio.purchase.model.AlbumPayResult;
import com.kaolafm.kradio.purchase.util.CountDownUtils;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.PayMethod;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;


public class AlbumRMBFragment extends BaseFragment<AlbumRMBPresenter> implements View.OnClickListener, IAlbumRMBView {
    private TextView tv_tag, tv_order_rmb_title, tv_order_rmb_tag,
            tv_order_rmb_origin_price, dollerCurrent, tv_order_rmb_current_price, tv_order_rmb_current, tv_count_down, purchase_notice_tip_tv;
    private FrameLayout purchaseNoticeContent;
    private ImageView iv_qrcode, iv_order_info_image, iv_tag, iv_qrcode_refresh;
    private CountDownTimer mCountDownTimer;
    private ConstraintLayout ll_order_rmb_origin_price;
    private LinearLayout ll_qrcode_failed;
    private TextView tv_qrcode_failed;
    private View purchasNoticeTip;

    private static final String PAY_SUBJECT_ID_KEY = "paySubjectId";
    private PlayItem mPlayItem;
    private Long mRmbPrice;

    /**
     * 定时轮询二维码扫码结果
     */
    private static final int PERIOD = 2 * 1000;
    private static final int DELAY = 1 * 1000;
    private Disposable mDisposable;// 二维码生成后开启定时任务，支付成功，退出页面，重新请求都需要关闭定时任务。
    AnimationDrawable mAnimationDrawable;

    public static AlbumRMBFragment newInstance(PlayItem playItem) {
        AlbumRMBFragment fragment = new AlbumRMBFragment();
        Bundle args = new Bundle();
        args.putParcelable(PAY_SUBJECT_ID_KEY, playItem);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initArgs() {
        Bundle bundle = getArguments();
        mPlayItem = bundle.getParcelable(PAY_SUBJECT_ID_KEY);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.order_content_rmb_base;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void initView(View view) {
        purchasNoticeTip = view.findViewById(R.id.purchas_notice_tip);
        purchasNoticeTip.setOnClickListener(this);
        purchaseNoticeContent = view.findViewById(R.id.tv_buy_notice_1);
        purchaseNoticeContent.setOnClickListener(this);
        iv_tag = view.findViewById(R.id.iv_tag);
        iv_qrcode = view.findViewById(R.id.iv_qrcode);
        iv_qrcode.setOnClickListener(this);
        iv_order_info_image = view.findViewById(R.id.iv_order_info_image);
        tv_tag = view.findViewById(R.id.tv_tag);
        tv_order_rmb_title = view.findViewById(R.id.tv_order_rmb_title);
        tv_order_rmb_tag = view.findViewById(R.id.tv_order_rmb_tag);
        tv_order_rmb_origin_price = view.findViewById(R.id.tv_order_rmb_origin_price);
        dollerCurrent = view.findViewById(R.id.dollerCurrent);
        tv_order_rmb_current_price = view.findViewById(R.id.tv_order_rmb_current_price);
        tv_order_rmb_current = view.findViewById(R.id.tv_order_rmb_current);
        ll_order_rmb_origin_price = view.findViewById(R.id.ll_order_rmb_origin_price);
        tv_count_down = view.findViewById(R.id.tv_count_down);
        ll_qrcode_failed = view.findViewById(R.id.ll_qrcode_failed);
        ll_qrcode_failed.setOnClickListener(this);
        iv_qrcode_refresh = view.findViewById(R.id.iv_qrcode_refresh);
        iv_qrcode_refresh.setImageResource(R.drawable.order_loading);
        mAnimationDrawable = (AnimationDrawable) iv_qrcode_refresh.getDrawable();
        tv_qrcode_failed = view.findViewById(R.id.tv_qrcode_failed);
        purchase_notice_tip_tv = view.findViewById(R.id.purchase_notice_tip_tv);

        if (mPlayItem != null) {
            mPresenter.getAlbumInfoDatas(Long.valueOf(mPlayItem.getAlbumId()));
        }

        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_ALBUM_ORDER_PURCHASE_NOTES, purchase_notice_tip_tv.getText().toString(), ReportParameterManager.getInstance().getPage()
                    , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ALBUM_ORDER, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
        }
    }

    @Override
    protected AlbumRMBPresenter createPresenter() {
        return new AlbumRMBPresenter(this);
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.ll_qrcode_failed) {//id == R.id.iv_qrcode ||
            tv_count_down.setVisibility(View.GONE);
            if (Long.valueOf(mPlayItem.getAlbumId()) != null) {
                //重新请求二维码，关闭上次的定时任务
                if (mDisposable != null) mDisposable.dispose();
                tv_qrcode_failed.setText(ResUtil.getString(R.string.user_qr_loading));
                mAnimationDrawable.start();
                mPresenter.getQrCodeData(Long.valueOf(mPlayItem.getAlbumId()), mRmbPrice);
            }
        } else if (id == R.id.purchas_notice_tip) {
            if (purchaseNoticeContent.getVisibility() != View.VISIBLE) {
                purchaseNoticeContent.setVisibility(View.VISIBLE);
            } else {
                purchaseNoticeContent.setVisibility(View.GONE);
            }
            PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            if (curPlayItem != null) {
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_ALBUM_ORDER_PURCHASE_NOTES, purchase_notice_tip_tv.getText().toString(), ReportParameterManager.getInstance().getPage()
                        , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_ALBUM_ORDER, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
            }
        } else if (id == R.id.tv_buy_notice_1) {
            purchaseNoticeContent.setVisibility(View.GONE);
        }
    }

    @Override
    public void onDestroy() {
        //关闭购买弹窗时，关闭定时任务
        if (mDisposable != null) mDisposable.dispose();
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        super.onDestroy();
    }

    @Override
    public boolean useEventBus() {
        return false;
    }

    /**
     * 单独刷新角标显示，用于主题切换时调用
     */
    public void refreshCornerIcon(int vip, int fine) {
        if (iv_tag != null) {
            VipCornerUtil.setVipCorner(iv_tag, vip, fine);
        }
    }

    @Override
    public void showQrCodeContent(QRCodeInfo qrCodeInfo) {
        showQrCodeSucessedUI(qrCodeInfo);

        mDisposable = Observable.interval(DELAY, PERIOD, TimeUnit.MILLISECONDS)
                .map((aLong -> aLong + 1))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> mPresenter.checkQrCodeStatus(qrCodeInfo.getQrCodeId()));
    }

    @Override
    public void showQrCodeLoading() {

    }

    @Override
    public void hideQrCodeLoading() {

    }

    @Override
    public void showQrCodeError(String error, boolean clickToRetry) {
        showQrCodeFailedUI(error);
    }

    @Override
    public void hideQrCodeError() {

    }

    @Override
    public void paySucessed(PurchaseSucess purchaseSucess) {
        if (purchaseSucess == null || purchaseSucess.getStatus() == null) {
            return;
        }
        if (purchaseSucess.getStatus() == 1) {
            //支付成功后，关闭定时任务
            if (mDisposable != null) mDisposable.dispose();
            PayManager.getInstance().onPayResult(new AlbumPayResult(purchaseSucess, mPlayItem));
            ((AlbumOrderDialogFragment) getParentFragment()).dismissAllowingStateLoss();
            PayResDialog.show(getActivity(), true, "支付成功");
        }
    }

    @Override
    public void payFailed(String error) {
        PayResDialog.show(getActivity(), false, error);
//        PurchaseSucess purchaseSucess = new PurchaseSucess();
//        purchaseSucess.setStatus(0);
//        PayResDialog.show(getActivity(), purchaseSucess);
//        PayManager.getInstance().onPayResult(new AlbumPayResult(purchaseSucess, mPlayItem));
    }

    private void showQrCodeSucessedUI(QRCodeInfo qrCodeInfo) {
        if (qrCodeInfo == null) {
            return;
        }
        mAnimationDrawable.stop();
        ImageLoader.getInstance().displayImage(getActivity(), qrCodeInfo.getQrCodeImg(), iv_qrcode);
        iv_qrcode.setVisibility(View.VISIBLE);
        ll_qrcode_failed.setVisibility(View.GONE);
        tv_count_down.setVisibility(View.VISIBLE);
        mCountDownTimer = new CountDownTimer((qrCodeInfo.getExpireTime() == null ? 0 : qrCodeInfo.getExpireTime()) * 1000, 1000) {

            @Override
            public void onTick(long millisUntilFinished) {
                tv_count_down.setText(CountDownUtils.FormatMiss(millisUntilFinished / 1000));
            }

            @Override
            public void onFinish() {
//                tv_count_down.setEnabled(true);
                tv_count_down.setVisibility(View.GONE);
                showQrCodeFailedUI("二维码已过期");
            }
        };
        mCountDownTimer.start();
    }

    private void showQrCodeFailedUI(String error) {
        mAnimationDrawable.stop();
        if ("当前网络异常".equals(error)) {
            ll_qrcode_failed.setVisibility(View.VISIBLE);
            tv_qrcode_failed.setText(ResUtil.getString(R.string.comprehensive_order_user_qr_error_no_network_msg));
            tv_qrcode_failed.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m20));
        } else {
            ll_qrcode_failed.setVisibility(View.VISIBLE);
            tv_qrcode_failed.setText(error + "\n点击重试");
            tv_qrcode_failed.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m22));
        }

        iv_qrcode.setVisibility(View.GONE);
        tv_count_down.setVisibility(View.GONE);
    }

    @Override
    public void showAlbumInfoContent(AlbumDetails albumDetails) {
        ImageLoader.getInstance().displayImage(getActivity(), albumDetails.getImg(), iv_order_info_image);
        /*
        if(albumDetails.getFine()==1){
            tv_tag.setVisibility(View.VISIBLE);
        }else {
            tv_tag.setVisibility(View.GONE);
        }
        if(mPlayItem.getVip()==1){
            iv_tag.setVisibility(View.VISIBLE);
        }else {
            iv_tag.setVisibility(View.GONE);
        }*/
        VipCornerUtil.setVipCorner(iv_tag, mPlayItem.getVip(), albumDetails.getFine());
        tv_order_rmb_title.setText(albumDetails.getName());

        List<PayMethod> list = albumDetails.getPayMethod();
        if (list != null && list.size() > 0) {
            for (PayMethod method : list) {
                if (method.getPayType() == 1) {
                    if (method.getBuyNotice() == null) {
                        tv_order_rmb_tag.setVisibility(View.GONE);
                    } else {
                        tv_order_rmb_tag.setText(method.getBuyNotice());
                        tv_order_rmb_tag.setVisibility(View.VISIBLE);
                    }
                    if (method.getCurrentPrice() == null) {
                        tv_order_rmb_title.setMaxLines(3);
                        ll_order_rmb_origin_price.setVisibility(View.GONE);
                        tv_order_rmb_current.setText("价格");
                        mRmbPrice = method.getOriginPrice();
                        tv_order_rmb_origin_price.setText(String.format(ResUtil.getString(R.string.comprehensive_order_origin_price), MoneyUtils.changeF2Y(method.getOriginPrice())));
                        tv_order_rmb_current_price.setText(MoneyUtils.changeF2Y(method.getOriginPrice()));
                        updateViewsWhenShowPrice(false);
                    } else {
                        tv_order_rmb_title.setMaxLines(2);
                        ll_order_rmb_origin_price.setVisibility(View.VISIBLE);
                        tv_order_rmb_current.setText("折扣价");
                        mRmbPrice = method.getCurrentPrice();
                        tv_order_rmb_origin_price.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG); //中划线;
                        tv_order_rmb_origin_price.setText(String.format(ResUtil.getString(R.string.comprehensive_order_origin_price), MoneyUtils.changeF2Y(method.getOriginPrice())));
                        tv_order_rmb_current_price.setText(MoneyUtils.changeF2Y(method.getCurrentPrice()));
                        updateViewsWhenShowPrice(true);

                    }
                }
            }
        }

        tv_qrcode_failed.setText(ResUtil.getString(R.string.user_qr_loading));
        mAnimationDrawable.start();
        mPresenter.getQrCodeData(Long.valueOf(mPlayItem.getAlbumId()), mRmbPrice);
    }

    /**
     * @param discountPrice 是否展示折扣价
     */
    private void updateViewsWhenShowPrice(boolean discountPrice) {
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) dollerCurrent.getLayoutParams();
        if (discountPrice) {
            dollerCurrent.setTextColor(ResUtil.getColor(R.color.comprehensive_order_money_color));
            tv_order_rmb_current_price.setTextColor(ResUtil.getColor(R.color.comprehensive_order_money_color));
            layoutParams.setMarginStart(ResUtil.getDimen(R.dimen.m2));
        } else {
            dollerCurrent.setTextColor(ResUtil.getColor(R.color.comprehensive_order_primary_color));
            tv_order_rmb_current_price.setTextColor(ResUtil.getColor(R.color.comprehensive_order_primary_color));
            layoutParams.setMarginStart(ResUtil.getDimen(R.dimen.m6));
        }
        dollerCurrent.setLayoutParams(layoutParams);
    }

    @Override
    public void showAlbumInfoLoading() {

    }

    @Override
    public void hideAlbumInfoLoading() {

    }

    @Override
    public void showAlbumInfoError(String error, boolean clickToRetry) {

    }

    @Override
    public void hideAlbumInfoError() {

    }

}
