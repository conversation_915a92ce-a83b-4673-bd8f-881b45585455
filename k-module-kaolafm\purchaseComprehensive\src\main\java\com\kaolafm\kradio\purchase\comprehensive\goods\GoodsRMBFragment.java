package com.kaolafm.kradio.purchase.comprehensive.goods;

import android.graphics.Paint;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.comprehensive.base.PayResDialog;
import com.kaolafm.kradio.purchase.goods.GoodsRMBPresenter;
import com.kaolafm.kradio.purchase.goods.IGoodsRMBView;
import com.kaolafm.kradio.purchase.model.GoodsPayResult;
import com.kaolafm.kradio.purchase.util.CountDownUtils;
import com.kaolafm.kradio.purchase.util.MoneyUtils;
import com.kaolafm.opensdk.api.live.model.GoodsDetails;
import com.kaolafm.opensdk.api.media.model.PayMethod;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

import static com.kaolafm.report.util.ReportConstants.DIALOG_ID_LIVE_ROOM_MERCHANDISE_PURCHASE;


public class GoodsRMBFragment extends BaseFragment<GoodsRMBPresenter> implements View.OnClickListener, IGoodsRMBView {
    private static final String TAG = "GoodsRMBFragment";

    private TextView tv_tag, tv_order_rmb_title, tv_order_rmb_tag,
            tv_order_rmb_origin_price, dollerCurrent, tv_order_rmb_current_price, tv_order_rmb_current, tv_count_down;
    private FrameLayout purchaseNoticeContent;
    private ImageView iv_qrcode, iv_order_info_image, iv_tag, iv_qrcode_refresh;
    private CountDownTimer mCountDownTimer;
    private ConstraintLayout ll_order_rmb_origin_price;
    private LinearLayout ll_qrcode_failed;
    private TextView tv_qrcode_failed, purchase_notice_tip_tv;
    private View purchasNoticeTip;

    private static final String PAY_SUBJECT_ID_KEY = "paySubjectId";
    private Long mGoodsId;
    private Long mRmbPrice;

    /**
     * 定时轮询二维码扫码结果
     */
    private static final int PERIOD = 2 * 1000;
    private static final int DELAY = 1 * 1000;
    private Disposable mDisposable;// 二维码生成后开启定时任务，支付成功，退出页面，重新请求都需要关闭定时任务。
    AnimationDrawable mAnimationDrawable;

    public static GoodsRMBFragment newInstance(Long goodsId) {
        GoodsRMBFragment fragment = new GoodsRMBFragment();
        Bundle args = new Bundle();
        args.putLong(PAY_SUBJECT_ID_KEY, goodsId);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initArgs() {
        Bundle bundle = getArguments();
        mGoodsId = bundle.getLong(PAY_SUBJECT_ID_KEY);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.order_content_rmb_base;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void initView(View view) {
        purchasNoticeTip = view.findViewById(R.id.purchas_notice_tip);
        purchasNoticeTip.setOnClickListener(this);
        purchaseNoticeContent = view.findViewById(R.id.tv_buy_notice_1);
        purchaseNoticeContent.setOnClickListener(this);
        iv_tag = view.findViewById(R.id.iv_tag);
        iv_qrcode = view.findViewById(R.id.iv_qrcode);
        iv_qrcode.setOnClickListener(this);
        iv_order_info_image = view.findViewById(R.id.iv_order_info_image);
        tv_tag = view.findViewById(R.id.tv_tag);
        tv_order_rmb_title = view.findViewById(R.id.tv_order_rmb_title);
        tv_order_rmb_tag = view.findViewById(R.id.tv_order_rmb_tag);
        tv_order_rmb_origin_price = view.findViewById(R.id.tv_order_rmb_origin_price);
        dollerCurrent = view.findViewById(R.id.dollerCurrent);
        tv_order_rmb_current_price = view.findViewById(R.id.tv_order_rmb_current_price);
        tv_order_rmb_current = view.findViewById(R.id.tv_order_rmb_current);
        ll_order_rmb_origin_price = view.findViewById(R.id.ll_order_rmb_origin_price);
        tv_count_down = view.findViewById(R.id.tv_count_down);
        ll_qrcode_failed = view.findViewById(R.id.ll_qrcode_failed);
        ll_qrcode_failed.setOnClickListener(this);
        iv_qrcode_refresh = view.findViewById(R.id.iv_qrcode_refresh);
        iv_qrcode_refresh.setImageResource(R.drawable.order_loading);
        mAnimationDrawable = (AnimationDrawable) iv_qrcode_refresh.getDrawable();
        tv_qrcode_failed = view.findViewById(R.id.tv_qrcode_failed);
        purchase_notice_tip_tv = view.findViewById(R.id.purchase_notice_tip_tv);

        if (mGoodsId != null) {
            Log.d(TAG, "initView, mGoodsId is " + mGoodsId);
            mPresenter.getGoodsInfoDatas(mGoodsId);
        }

    }

    @Override
    protected GoodsRMBPresenter createPresenter() {
        return new GoodsRMBPresenter(this);
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.ll_qrcode_failed) {//id == R.id.iv_qrcode ||
            tv_count_down.setVisibility(View.GONE);
            if (mGoodsId != 0) {
                //重新请求二维码，关闭上次的定时任务
                if (mDisposable != null) mDisposable.dispose();
                tv_qrcode_failed.setText(ResUtil.getString(R.string.user_qr_loading));
                mAnimationDrawable.start();
                mPresenter.getQrCode(mGoodsId, mRmbPrice, 2);
            }
        } else if (id == R.id.purchas_notice_tip) {
            if (purchaseNoticeContent.getVisibility() != View.VISIBLE) {
                purchaseNoticeContent.setVisibility(View.VISIBLE);
            } else {
                purchaseNoticeContent.setVisibility(View.GONE);
            }
            //数据上报
            String radioId = null, audioId = null;
            PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            if (curPlayItem != null) {
                radioId = curPlayItem.getRadioId();
                audioId = String.valueOf(curPlayItem.getAudioId());
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_MERCHANDISE_PURCHASE_NOTICE, purchase_notice_tip_tv.getText().toString(), getPageId()
                    , ReportConstants.CONTROL_TYPE_SCREEN, DIALOG_ID_LIVE_ROOM_MERCHANDISE_PURCHASE, radioId, audioId, String.valueOf(mGoodsId), null));
        }else if (id == R.id.tv_buy_notice_1) {
            purchaseNoticeContent.setVisibility(View.GONE);
        }
    }

    @Override
    public void onDestroy() {
        //关闭购买弹窗时，关闭定时任务
        if (mDisposable != null) mDisposable.dispose();
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        super.onDestroy();
    }

    @Override
    public boolean useEventBus() {
        return false;
    }

    @Override
    public void showQrCodeContent(QRCodeInfo qrCodeInfo) {
        Log.d(TAG, "showQrCodeContent, qrCodeInfo.getStatus() is " + qrCodeInfo.getStatus());
        Log.d(TAG, "showQrCodeContent, qrCodeInfo.getQrCodeImg() is " + qrCodeInfo.getQrCodeImg());
        showQrCodeSucessedUI(qrCodeInfo);

        mDisposable = Observable.interval(DELAY, PERIOD, TimeUnit.MILLISECONDS)
                .map((aLong -> aLong + 1))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> mPresenter.checkQrCodeStatus(qrCodeInfo.getQrCodeId()));
    }

    @Override
    public void showQrCodeLoading() {

    }

    @Override
    public void hideQrCodeLoading() {

    }

    @Override
    public void onResume() {
        super.onResume();
        //事件上报
        String radioId = null, audioId = null;
        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            radioId = curPlayItem.getRadioId();
            audioId = String.valueOf(curPlayItem.getAudioId());
        }
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_LIVE_ROOM_MERCHANDISE_PURCHASE_NOTICE, purchase_notice_tip_tv.getText().toString(), getPageId()
                , ReportConstants.CONTROL_TYPE_SCREEN, DIALOG_ID_LIVE_ROOM_MERCHANDISE_PURCHASE, radioId, audioId, String.valueOf(mGoodsId), null));
    }

    @Override
    public void showQrCodeError(String error, boolean clickToRetry) {
        showQrCodeFailedUI(error);
    }

    @Override
    public void hideQrCodeError() {

    }

    @Override
    public void paySucessed(PurchaseSucess purchaseSucess) {
        if(BuildConfig.DEBUG){
            Log.d(TAG, "paySucessed");
        }
        if (purchaseSucess == null) {
            if(BuildConfig.DEBUG){
                Log.d(TAG, "paySucessed, purchaseSucess is null");
            }
            return;
        }
        if (purchaseSucess.getStatus() == null) {
            if(BuildConfig.DEBUG){
                Log.d(TAG, "paySucessed, purchaseSucess.getStatus() is null");
            }
            return;
        }
        if (purchaseSucess.getStatus() == 1) {
            if(BuildConfig.DEBUG){
                Log.d(TAG, "paySucessed: status=1");
            }
            //支付成功后，关闭定时任务
            if (mDisposable != null) mDisposable.dispose();
            PayManager.getInstance().onPayResult(new GoodsPayResult(purchaseSucess));
            ((GoodsOrderDialogFragment) getParentFragment()).dismissAllowingStateLoss();
            GoodsPaySuccessedDialog.newInstance().show(getActivity().getSupportFragmentManager(), "goods_pay_success");
//            PayResDialog.show(getActivity(), true, "支付成功");
        }
    }

    @Override
    public void payFailed(String error) {
        PayResDialog.show(getActivity(), false, error);
//        PurchaseSucess purchaseSucess = new PurchaseSucess();
//        purchaseSucess.setStatus(0);
//        PayResDialog.show(getActivity(), purchaseSucess);
//        PayManager.getInstance().onPayResult(new AlbumPayResult(purchaseSucess, mPlayItem));
    }

    private void showQrCodeSucessedUI(QRCodeInfo qrCodeInfo) {
        if (qrCodeInfo == null) {
            return;
        }
        mAnimationDrawable.stop();
        ImageLoader.getInstance().displayImage(getActivity(), qrCodeInfo.getQrCodeImg(), iv_qrcode);
        iv_qrcode.setVisibility(View.VISIBLE);
        ll_qrcode_failed.setVisibility(View.GONE);
        tv_count_down.setVisibility(View.VISIBLE);
        mCountDownTimer = new CountDownTimer((qrCodeInfo.getExpireTime() == null ? 0 : qrCodeInfo.getExpireTime()) * 1000, 1000) {

            @Override
            public void onTick(long millisUntilFinished) {
                tv_count_down.setText(CountDownUtils.FormatMiss(millisUntilFinished / 1000));
            }

            @Override
            public void onFinish() {
//                tv_count_down.setEnabled(true);
                tv_count_down.setVisibility(View.GONE);
                showQrCodeFailedUI("二维码已过期");
            }
        };
        mCountDownTimer.start();
    }

    private void showQrCodeFailedUI(String error) {
        mAnimationDrawable.stop();
        if ("当前网络异常".equals(error)) {
            ll_qrcode_failed.setVisibility(View.VISIBLE);
            tv_qrcode_failed.setText(ResUtil.getString(R.string.comprehensive_order_user_qr_error_no_network_msg));
            tv_qrcode_failed.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m20));
        } else {
            ll_qrcode_failed.setVisibility(View.VISIBLE);
            tv_qrcode_failed.setText(error + "\n点击重试");
            tv_qrcode_failed.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m22));
        }

        iv_qrcode.setVisibility(View.GONE);
        tv_count_down.setVisibility(View.GONE);
    }

    @Override
    public void showGoodsInfoContent(GoodsDetails goodsDetails) {
        Log.d(TAG, "showGoodsInfoContent, goodsDetails.id is " + goodsDetails.getId());
        ImageLoader.getInstance().displayImage(getActivity(), goodsDetails.getPicUrl(), iv_order_info_image);
        /*
        if(albumDetails.getFine()==1){
            tv_tag.setVisibility(View.VISIBLE);
        }else {
            tv_tag.setVisibility(View.GONE);
        }
        if(mPlayItem.getVip()==1){
            iv_tag.setVisibility(View.VISIBLE);
        }else {
            iv_tag.setVisibility(View.GONE);
        }*/
//        VipCornerUtil.setVipCorner(iv_tag, mPlayItem.getVip(), albumDetails.getFine());
        tv_order_rmb_title.setText(goodsDetails.getName());

        List<PayMethod> list = goodsDetails.getPayMethod();
        if (list != null && list.size() > 0) {
            for (PayMethod method : list) {
                if (method.getPayType() == 1) {
                    if (TextUtils.isEmpty(method.getBuyNotice())) {
                        tv_order_rmb_tag.setVisibility(View.GONE);
                    } else {
                        tv_order_rmb_tag.setText(method.getBuyNotice());
                        tv_order_rmb_tag.setVisibility(View.VISIBLE);
                    }
                    if (method.getCurrentPrice() == null) {
                        tv_order_rmb_title.setMaxLines(3);
                        ll_order_rmb_origin_price.setVisibility(View.GONE);
                        tv_order_rmb_current.setText("价格");
                        mRmbPrice = method.getOriginPrice();
                        tv_order_rmb_origin_price.setText(MoneyUtils.changeF2Y(method.getOriginPrice()));
                        tv_order_rmb_current_price.setText(MoneyUtils.changeF2Y(method.getOriginPrice()));
                        dollerCurrent.setTextColor(ResUtil.getColor(R.color.comprehensive_order_primary_color));
                        tv_order_rmb_current_price.setTextColor(ResUtil.getColor(R.color.comprehensive_order_primary_color));
                    } else {
                        tv_order_rmb_title.setMaxLines(2);
                        ll_order_rmb_origin_price.setVisibility(View.VISIBLE);
                        tv_order_rmb_current.setText("折扣价");
                        mRmbPrice = method.getCurrentPrice();
                        tv_order_rmb_origin_price.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG); //中划线;
                        tv_order_rmb_origin_price.setText(MoneyUtils.changeF2Y(method.getOriginPrice()));
                        tv_order_rmb_current_price.setText(MoneyUtils.changeF2Y(method.getCurrentPrice()));
                        dollerCurrent.setTextColor(ResUtil.getColor(R.color.comprehensive_order_money_color));
                        tv_order_rmb_current_price.setTextColor(ResUtil.getColor(R.color.comprehensive_order_money_color));
                    }
                }
            }
        }

        tv_qrcode_failed.setText(ResUtil.getString(R.string.user_qr_loading));
        mAnimationDrawable.start();
        mPresenter.getQrCode(mGoodsId, mRmbPrice, 2);
    }

    @Override
    public void showGoodsInfoLoading() {

    }

    @Override
    public void hideGoodsInfoLoading() {

    }

    @Override
    public void showGoodsInfoError(String error, boolean clickToRetry) {

    }

    @Override
    public void hideGoodsInfoError() {

    }

}
