package com.kaolafm.kradio.user.comprehensive.ui;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.mvp.IPresenter;
import com.kaolafm.kradio.lib.base.ui.BaseViewPagerFragment;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.kradio.user.comprehensive.order.MyOrderFragment;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.PageShowReportEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import me.yokeyword.fragmentation.ISupportFragment;

/**
 * 用户信息
 */
public class ComprehensiveUserInfoFragment extends BaseViewPagerFragment implements View.OnClickListener {

    LinearLayout user_tv_ll;
    TextView user_info_tv;
    TextView user_order_tv;
    private DynamicComponent mUserLoginComponent;
    private UserLoginFragment userLoginFragment;
    private MyOrderFragment myOrderFragment;
    private UnLoginFragment unLoginFragment;
    private int index = 0;//当前选中的tab

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_user_info;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected IPresenter createPresenter() {
        return null;
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {

    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mUserLoginComponent = new UserLoginComponent();
        ComponentUtil.addObserver(UserComponentConst.NAME, mUserLoginComponent);
        EventBus.getDefault().register(this);
    }

    private class UserLoginComponent implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "UserLoginComponent$ComprehensiveUserInfoFragment";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            if (UserStateObserverProcessorConst.USER_LOGOUT.equals(actionName) || UserStateObserverProcessorConst.USER_LOGIN.equals(actionName)) {
                upDateLogin();
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }

    @Override
    public void initView(View view) {

        user_tv_ll=view.findViewById(R.id.user_tv_ll);
        user_info_tv=view.findViewById(R.id.user_info_tv);
        user_order_tv=view.findViewById(R.id.user_order_tv);

        user_info_tv.setOnClickListener(this);
        user_order_tv.setOnClickListener(this);

        // 初始化时设置按钮状态，使用新版主题系统
        setBtnState();
    }

    /**
     * 刷新登录状态
     */
    public void upDateLogin() {
        ISupportFragment target = null;
        if (UserInfoManager.getInstance().isUserBound()) {
            if (userLoginFragment == null) {
                userLoginFragment = new UserLoginFragment();
            }
            user_info_tv.setEnabled(true);
            user_order_tv.setEnabled(true);
            user_order_tv.setTextColor(ResUtil.getColor(R.color.mine_user_btn_unselece_color));
            target = userLoginFragment;
        } else {
            if (unLoginFragment == null) {
                unLoginFragment = new UnLoginFragment();
            }
            target = unLoginFragment;
            user_order_tv.setEnabled(false);
            user_info_tv.setEnabled(false);
            user_order_tv.setTextColor(ResUtil.getColor(R.color.mine_unlogin_order_text_color));
        }
        if (index == 0) {
            loadRootFragment(R.id.user_info_fl, target);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (AntiShake.check(id)) {
            return;
        }
        if (id == R.id.user_info_tv) {//用户信息
            if (userLoginFragment == null) {
                userLoginFragment = new UserLoginFragment();
            }
            loadRootFragment(R.id.user_info_fl, userLoginFragment);
            index = 0;
        } else if (id == R.id.user_order_tv) {//我的订单
            //我的订单
            if (myOrderFragment == null) {
                myOrderFragment = (MyOrderFragment) RouterManager.getInstance().getRouterFragment(RouterConstance.ORDER_COMPREHENSIVE_URL);
            }
            loadRootFragment(R.id.user_info_fl, myOrderFragment);
            index = 1;
        }
        setBtnState();
    }

    @Override
    public void onUserVisible() {
        super.onUserVisible();
        upDateLogin();
        // 无论当前选中哪个标签，都需要更新按钮状态以确保颜色正确
        setBtnState();
        startTime = System.currentTimeMillis();
    }

    private void setBtnState() {
        setBtnStateInternal();
    }

    private void setBtnStateInternal() {
        // 使用皮肤框架判断当前主题
        boolean isDayMode = SkinHelper.isDayMode();
        // 检查登录状态
        boolean isUserBound = UserInfoManager.getInstance().isUserBound();

        if (index == 0) {
            // 账号信息选中状态
            if (isDayMode) {
                user_info_tv.setBackground(ResUtil.getDrawable(R.drawable.comprehensive_account_info_bt_day));
                user_info_tv.setTextColor(ResUtil.getColor(R.color.mine_user_btn_selece_color_day));
                // 根据登录状态设置订单按钮颜色
                if (isUserBound) {
                    user_order_tv.setTextColor(ResUtil.getColor(R.color.mine_user_btn_unselece_color_day));
                } else {
                    user_order_tv.setTextColor(ResUtil.getColor(R.color.mine_unlogin_order_text_color));
                }
            } else {
                user_info_tv.setBackground(ResUtil.getDrawable(R.drawable.comprehensive_account_info_bt));
                user_info_tv.setTextColor(ResUtil.getColor(R.color.mine_user_btn_selece_color));
                // 根据登录状态设置订单按钮颜色
                if (isUserBound) {
                    user_order_tv.setTextColor(ResUtil.getColor(R.color.mine_user_btn_unselece_color));
                } else {
                    user_order_tv.setTextColor(ResUtil.getColor(R.color.mine_unlogin_order_text_color));
                }
            }
            user_order_tv.setBackground(null);
        } else {
            // 我的订单选中状态
            if (isDayMode) {
                user_order_tv.setBackground(ResUtil.getDrawable(R.drawable.comprehensive_account_info_bt_day));
                user_order_tv.setTextColor(ResUtil.getColor(R.color.mine_user_btn_selece_color_day));
                user_info_tv.setTextColor(ResUtil.getColor(R.color.mine_user_btn_unselece_color_day));
            } else {
                user_order_tv.setBackground(ResUtil.getDrawable(R.drawable.comprehensive_account_info_bt));
                user_order_tv.setTextColor(ResUtil.getColor(R.color.mine_user_btn_selece_color));
                user_info_tv.setTextColor(ResUtil.getColor(R.color.mine_user_btn_unselece_color));
            }
            user_info_tv.setBackground(null);
        }
    }

    /**
     * 延迟设置按钮状态，确保在皮肤框架应用后执行
     */
    private void setBtnStateDelayed() {
        new Handler(Looper.getMainLooper()).postDelayed(this::setBtnStateInternal, 300);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        ComponentUtil.removeObserver(UserComponentConst.NAME, mUserLoginComponent);
        EventBus.getDefault().unregister(this);
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_MINE_USER;
    }


    @Override
    public void onUserInvisible() {
        super.onUserInvisible();
        reportPageShowEvent();
    }

    /**
     * 页面曝光事件上报
     */
    @Override
    public void reportPageShowEvent() {
        long duration = System.currentTimeMillis() - startTime;
        if (TextUtils.isEmpty(getPageId()) || startTime < 0 || duration < 300) {
            return;
        }

        PageShowReportEvent event = new PageShowReportEvent();
        event.setPage(getPageId());
        event.setPageId(getPageId());
        event.setPageTime(String.valueOf(duration));
        ReportHelper.getInstance().addEvent(event);
        Log.i(getClass().getSimpleName(), "report=" + duration);
        startTime = -1;
    }

    /**
     * 监听主题切换事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeChangeEvent(UserCenterInter.ThemeChangeEvent event) {
        Log.d("ComprehensiveUserInfoFragment", "收到主题变化事件: " + (event != null ? event.getTheme() : "null"));

        String theme = (event == null) ? "" : event.getTheme();
        if (TextUtils.isEmpty(theme)) {
            return;
        }

        // 处理isSameTheme事件：确认当前主题状态
        if (SkinHelper.IS_SAME_THEME.equals(theme)) {
            Log.d("ComprehensiveUserInfoFragment", "收到isSameTheme事件，延迟刷新UI状态");
            setBtnStateDelayed();
            upDateLogin();
            return;
        }

        // 根据皮肤框架的主题事件更新UI
        if (SkinHelper.NIGHT_SKIN.equals(theme) || SkinHelper.DAY_SKIN.equals(theme)) {
            Log.d("ComprehensiveUserInfoFragment", "主题切换为: " + theme + "，延迟更新按钮状态");
            setBtnStateDelayed();
            upDateLogin();
        }
    }
}
