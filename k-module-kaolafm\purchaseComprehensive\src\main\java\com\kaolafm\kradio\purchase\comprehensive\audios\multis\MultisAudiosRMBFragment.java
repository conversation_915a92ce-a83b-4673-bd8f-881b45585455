package com.kaolafm.kradio.purchase.comprehensive.audios.multis;

import android.graphics.Paint;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.TypedValue;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.kradio.common.comprehensive.web.WebViewActivity;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.comprehensive.audios.AudiosOrderDialogFragment;
import com.kaolafm.kradio.purchase.comprehensive.base.PayResDialog;
import com.kaolafm.kradio.component.ui.base.utils.VipCornerUtil;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.audios.multis.IMultisAudiosRMBView;
import com.kaolafm.kradio.purchase.audios.multis.MultisAudiosRMBPresenter;
import com.kaolafm.kradio.purchase.audios.multis.MultisSelectData;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.purchase.model.AudiosPayResult;
import com.kaolafm.kradio.purchase.util.CountDownUtils;
import com.kaolafm.kradio.purchase.util.MoneyUtils;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;


public class MultisAudiosRMBFragment extends BaseFragment<MultisAudiosRMBPresenter> implements View.OnClickListener, IMultisAudiosRMBView {
    private TextView tv_tag, tv_order_rmb_title, tv_order_rmb_audio_count, tv_order_rmb_tag, dollerCurrent,
            tv_order_rmb_origin_price, tv_order_rmb_current_price, tv_order_rmb_current, tv_count_down, purchase_notice_tip_tv;
    private FrameLayout purchaseNoticeContent;
    private ImageView iv_qrcode, iv_order_info_image, iv_tag, iv_qrcode_refresh;
    private CountDownTimer mCountDownTimer;
    private LinearLayout ll_qrcode_failed;
    private ConstraintLayout ll_order_rmb_origin_price;
    private TextView tv_qrcode_failed;
    private View purchasNoticeTip;

    private MultisAudiosRMBPresenter mPresenter;

    private static final String PAY_SUBJECT_ID_KEY = "paySubjectId";
    private PlayItem mPlayItem;
    private MultisSelectData mMultisSelectData;
    private static final String PAY_AUDIOS_KEY = "audioIds";
    private static final String PAY_ALBUM_KEY = "albumId";
    private static final String PAY_ORIGIN_MONEY_KEY = "originMoney";
    private static final String PAY_CURRENT_MONEY_KEY = "currentMoney";
    private static final String PAY_MULTIS_SELECT_KEY = "multisSelect";
    private String mSelectedAudioIds = "";
    private Long mSelectedAlbumId = null;
    private String mSelectedBuyNotice = null;
    private Long mSelectedOriginMoney = null;
    private Long mSelectedCurrentMoney = null;
    private int mSelectedAudioCount = 0;

    AnimationDrawable mAnimationDrawable;
    /**
     * 定时轮询二维码扫码结果
     */
    private static final int PERIOD = 2 * 1000;
    private static final int DELAY = 1 * 1000;
    private Disposable mDisposable;// 二维码生成后开启定时任务，支付成功，退出页面，重新请求都需要关闭定时任务。

    public static MultisAudiosRMBFragment newInstance(PlayItem playItem, MultisSelectData multisSelectData) {
        MultisAudiosRMBFragment fragment = new MultisAudiosRMBFragment();
        Bundle args = new Bundle();
        args.putParcelable(PAY_SUBJECT_ID_KEY, playItem);
        args.putParcelable(PAY_MULTIS_SELECT_KEY, multisSelectData);
//        args.putString(PAY_AUDIOS_KEY, audioIds);
//        args.putLong(PAY_ALBUM_KEY, albumId);
//        args.putLong(PAY_ORIGIN_MONEY_KEY, originMoney);
//        args.putLong(PAY_CURRENT_MONEY_KEY, currentMoney);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.order_content_rmb_base;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected MultisAudiosRMBPresenter createPresenter() {
        mPresenter = new MultisAudiosRMBPresenter(this);
        return mPresenter;
    }

    @Override
    public void initView(View view) {
        Bundle bundle = getArguments();
        mPlayItem = bundle.getParcelable(PAY_SUBJECT_ID_KEY);
        mMultisSelectData = bundle.getParcelable(PAY_MULTIS_SELECT_KEY);
        mSelectedAudioIds = mMultisSelectData.getAudioIds();
        mSelectedAlbumId = mMultisSelectData.getAlbumId();
        mSelectedBuyNotice = mMultisSelectData.getBuyNotice();
        mSelectedOriginMoney = mMultisSelectData.getOriginMoney();
        mSelectedCurrentMoney = mMultisSelectData.getCurrentMoney();
        mSelectedAudioCount = mMultisSelectData.getAudioCount();
//        mSelectedAudioIds = bundle.getString(PAY_AUDIOS_KEY);
//        mSelectedAlbumId = bundle.getLong(PAY_ALBUM_KEY);
//        mSelectedOriginMoney = bundle.getLong(PAY_ORIGIN_MONEY_KEY);
//        mSelectedCurrentMoney = bundle.getLong(PAY_CURRENT_MONEY_KEY);

        purchasNoticeTip = view.findViewById(R.id.purchas_notice_tip);
        purchasNoticeTip.setOnClickListener(this);
        purchaseNoticeContent = view.findViewById(R.id.tv_buy_notice_1);
        purchaseNoticeContent.setOnClickListener(this);
        dollerCurrent = view.findViewById(R.id.dollerCurrent);

        iv_tag = view.findViewById(R.id.iv_tag);
        iv_qrcode = view.findViewById(R.id.iv_qrcode);
        iv_qrcode.setOnClickListener(this);
        iv_order_info_image = view.findViewById(R.id.iv_order_info_image);
        tv_tag = view.findViewById(R.id.tv_tag);
        tv_order_rmb_title = view.findViewById(R.id.tv_order_rmb_title);
        tv_order_rmb_audio_count = view.findViewById(R.id.tv_order_rmb_audio_count);
        tv_order_rmb_tag = view.findViewById(R.id.tv_order_rmb_tag);
        tv_order_rmb_origin_price = view.findViewById(R.id.tv_order_rmb_origin_price);
        tv_order_rmb_current_price = view.findViewById(R.id.tv_order_rmb_current_price);
        tv_order_rmb_current = view.findViewById(R.id.tv_order_rmb_current);
        ll_order_rmb_origin_price = view.findViewById(R.id.ll_order_rmb_origin_price);

        ll_qrcode_failed = view.findViewById(R.id.ll_qrcode_failed);
        ll_qrcode_failed.setOnClickListener(this);
        tv_qrcode_failed = view.findViewById(R.id.tv_qrcode_failed);
        tv_count_down = view.findViewById(R.id.tv_count_down);
        iv_qrcode_refresh = view.findViewById(R.id.iv_qrcode_refresh);
        iv_qrcode_refresh.setImageResource(R.drawable.order_loading);
        mAnimationDrawable = (AnimationDrawable) iv_qrcode_refresh.getDrawable();
        purchase_notice_tip_tv = view.findViewById(R.id.purchase_notice_tip_tv);

        ImageLoader.getInstance().displayImage(getActivity(), mPlayItem.getPicUrl(), iv_order_info_image);
        /*
        if(mPlayItem.getFine()==1){
            tv_tag.setText("精品");
            tv_tag.setVisibility(View.VISIBLE);
        }else {
            tv_tag.setVisibility(View.GONE);
        }
        if(mPlayItem.getVip()==1){
            iv_tag.setVisibility(View.VISIBLE);
        }else {
            iv_tag.setVisibility(View.GONE);
        }*/
        VipCornerUtil.setVipCorner(iv_tag, mPlayItem.getVip(), mPlayItem.getFine());

        if (mSelectedBuyNotice == null) {
            tv_order_rmb_tag.setVisibility(View.GONE);
        } else {
            tv_order_rmb_tag.setText(mSelectedBuyNotice);
            tv_order_rmb_tag.setVisibility(View.VISIBLE);
        }
        if (mSelectedCurrentMoney == null) {
            tv_order_rmb_title.setMaxLines(2);
            ll_order_rmb_origin_price.setVisibility(View.GONE);
            tv_order_rmb_current.setText("价格");
            tv_order_rmb_current_price.setText(MoneyUtils.changeF2Y(mSelectedOriginMoney));
            updateViewsWhenShowPrice(false);
        } else {
            tv_order_rmb_title.setMaxLines(1);
            ll_order_rmb_origin_price.setVisibility(View.VISIBLE);
            tv_order_rmb_current.setText("折扣价");
            tv_order_rmb_origin_price.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG); //中划线;
            tv_order_rmb_origin_price.setText(String.format(ResUtil.getString(R.string.comprehensive_order_origin_price), MoneyUtils.changeF2Y(mSelectedOriginMoney)));
            tv_order_rmb_current_price.setText(MoneyUtils.changeF2Y(mSelectedCurrentMoney));
            updateViewsWhenShowPrice(true);
        }
        setTitleWithAudioCount();
        if (mSelectedCurrentMoney != null) {
            tv_order_rmb_current_price.setText(MoneyUtils.changeF2Y(mSelectedCurrentMoney));
        }
        tv_qrcode_failed.setVisibility(View.GONE);
        mAnimationDrawable.start();
        mPresenter.getAudioQrCodeData(mSelectedAudioIds, mSelectedAlbumId, mSelectedCurrentMoney == null ? mSelectedOriginMoney : mSelectedCurrentMoney);

        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_AUDIO_ORDER_PURCHASE_NOTES, purchase_notice_tip_tv.getText().toString(), ReportParameterManager.getInstance().getPage()
                    , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_AUDIO_ORDER, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
        }
    }

    /**
     * @param discountPrice 是否展示折扣价
     */
    private void updateViewsWhenShowPrice(boolean discountPrice) {
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) dollerCurrent.getLayoutParams();
        if (discountPrice) {
            dollerCurrent.setTextColor(ResUtil.getColor(R.color.comprehensive_order_money_color));
            tv_order_rmb_current_price.setTextColor(ResUtil.getColor(R.color.comprehensive_order_money_color));
            layoutParams.setMarginStart(ResUtil.getDimen(R.dimen.m2));
        } else {
            dollerCurrent.setTextColor(ResUtil.getColor(R.color.comprehensive_order_primary_color));
            tv_order_rmb_current_price.setTextColor(ResUtil.getColor(R.color.comprehensive_order_primary_color));
            layoutParams.setMarginStart(ResUtil.getDimen(R.dimen.m6));
        }
        dollerCurrent.setLayoutParams(layoutParams);
    }

    private void setTitleWithAudioCount() {
        String audioCountStr = String.format(ResUtil.getString(R.string.comprehensive_order_multi_audio_title), mSelectedAudioCount);
        tv_order_rmb_title.setText(mPlayItem.getAlbumTitle());
        tv_order_rmb_audio_count.setText(audioCountStr);
        ViewUtil.setViewVisibility(tv_order_rmb_audio_count, View.VISIBLE);
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.ll_qrcode_failed) {//id == R.id.iv_qrcode ||
            tv_count_down.setVisibility(View.GONE);
            if (String.valueOf(mPlayItem.getAudioId()) != null) {
                //重新请求二维码，关闭上次的定时任务
                if (mDisposable != null) mDisposable.dispose();
                tv_qrcode_failed.setVisibility(View.GONE);
                mAnimationDrawable.start();
                mPresenter.getAudioQrCodeData(mSelectedAudioIds, Long.valueOf(mPlayItem.getAlbumId()), mSelectedCurrentMoney == null ? mSelectedOriginMoney : mSelectedCurrentMoney);
            }
        } else if (id == R.id.tv_rmb_buy_notice) {
            WebViewActivity.start(getActivity(), PayConst.BUY_NOTICE_URL);
        } else if (id == R.id.purchas_notice_tip) {
            if (purchaseNoticeContent.getVisibility() != View.VISIBLE) {
                purchaseNoticeContent.setVisibility(View.VISIBLE);
            } else {
                purchaseNoticeContent.setVisibility(View.GONE);
            }
            PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            if (curPlayItem != null) {
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_AUDIO_ORDER_PURCHASE_NOTES, purchase_notice_tip_tv.getText().toString(), ReportParameterManager.getInstance().getPage()
                        , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_AUDIO_ORDER, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
            }
        }else if (id == R.id.tv_buy_notice_1) {
            purchaseNoticeContent.setVisibility(View.GONE);
        }
    }

    @Override
    public void onDestroy() {
        //关闭购买弹窗时，关闭定时任务
        if (mDisposable != null) mDisposable.dispose();
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        super.onDestroy();
    }

    @Override
    public void showQrCodeContent(QRCodeInfo qrCodeInfo) {
        showQrCodeSucessedUI(qrCodeInfo);

        mDisposable = Observable.interval(DELAY, PERIOD, TimeUnit.MILLISECONDS)
                .map((aLong -> aLong + 1))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> mPresenter.checkQrCodeStatus(qrCodeInfo.getQrCodeId()));
    }

    @Override
    public void showQrCodeLoading() {

    }

    @Override
    public void hideQrCodeLoading() {

    }

    @Override
    public void showQrCodeError(String error, boolean clickToRetry) {
        showQrCodeFailedUI(error);
    }

    @Override
    public void hideQrCodeError() {

    }


    @Override
    public void paySucessed(PurchaseSucess purchaseSucess) {
        if (purchaseSucess == null || purchaseSucess.getStatus() == null) {
            return;
        }
        if (purchaseSucess.getStatus() == 1) {
            //支付成功后，关闭定时任务
            if (mDisposable != null) mDisposable.dispose();
            PayManager.getInstance().onPayResult(new AudiosPayResult(purchaseSucess, mPlayItem, mSelectedAudioIds, mSelectedAlbumId));
            ((AudiosOrderDialogFragment) getParentFragment().getParentFragment()).dismissAllowingStateLoss();
            PayResDialog.show(getActivity(), true, "支付成功");
        }
    }

    @Override
    public void payFailed(String error) {
        PayResDialog.show(getActivity(), false, error);
//        PurchaseSucess purchaseSucess = new PurchaseSucess();
//        purchaseSucess.setStatus(0);
//        PayManager.getInstance().onPayResult(new AudiosPayResult(purchaseSucess, mPlayItem, mMultisAudiosOrderAdapter.getSelectedIds(), mAudioDetailList.get(0).getAlbumId()));
    }


    private void showQrCodeSucessedUI(QRCodeInfo qrCodeInfo) {
        if (qrCodeInfo == null) {
            return;
        }
        mAnimationDrawable.stop();
        ImageLoader.getInstance().displayImage(getActivity(), qrCodeInfo.getQrCodeImg(), iv_qrcode);
        iv_qrcode.setVisibility(View.VISIBLE);
        ll_qrcode_failed.setVisibility(View.GONE);
        tv_count_down.setVisibility(View.VISIBLE);
        mCountDownTimer = new CountDownTimer((qrCodeInfo.getExpireTime() == null ? 0 : qrCodeInfo.getExpireTime()) * 1000, 1000) {

            @Override
            public void onTick(long millisUntilFinished) {
                tv_count_down.setText(CountDownUtils.FormatMiss(millisUntilFinished / 1000));
            }

            @Override
            public void onFinish() {
//                tv_count_down.setEnabled(true);
                tv_count_down.setVisibility(View.GONE);
                showQrCodeFailedUI("二维码已过期");
            }
        };
        mCountDownTimer.start();
    }

    private void showQrCodeFailedUI(String error) {
        mAnimationDrawable.stop();
        if ("当前网络异常".equals(error)) {
            ll_qrcode_failed.setVisibility(View.VISIBLE);
            tv_qrcode_failed.setText(ResUtil.getString(R.string.comprehensive_order_user_qr_error_no_network_msg));
            tv_qrcode_failed.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m20));
        } else {
            ll_qrcode_failed.setVisibility(View.VISIBLE);
            tv_qrcode_failed.setText(error + "\n点击重试");
            tv_qrcode_failed.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimen(R.dimen.m22));
        }
        iv_qrcode.setVisibility(View.GONE);
        tv_count_down.setVisibility(View.GONE);
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {

    }

    @Override
    protected void addFragmentRootViewPadding(View view) {

    }
}
