package com.kaolafm.kradio.purchase.comprehensive.vip;

import android.content.DialogInterface;
import android.content.res.Configuration;
import android.graphics.drawable.AnimationDrawable;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.component.ui.base.skin.SkinHelper;
import com.kaolafm.kradio.common.utils.FlavorUtil;
import com.kaolafm.kradio.common.comprehensive.web.WebViewActivity;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.purchase.comprehensive.base.PayResDialog;
import com.kaolafm.kradio.purchase.comprehensive.view.VipDividerItemDecoration;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioToastInter;
import com.kaolafm.kradio.lib.base.flavor.ProtocolViplinkInter;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.comprehensive.base.BaseComprehensiveOrderDialogFragment;
import com.kaolafm.kradio.purchase.util.CountDownUtils;
import com.kaolafm.kradio.purchase.model.VipPayResult;
import com.kaolafm.kradio.purchase.vip.IVipOrderView;
import com.kaolafm.kradio.purchase.vip.VipOrderPresenter;
import com.kaolafm.opensdk.api.purchase.model.PurchaseSucess;
import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.api.purchase.model.VipMeals;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.UIThreadUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.DialogExposureEvent;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;


public class VipOrderDialogFragment extends BaseComprehensiveOrderDialogFragment<VipOrderPresenter> implements View.OnClickListener, VipOrderAdapter.OnItemCheckListener, IVipOrderView {
    private ImageView iv_qrcode, iv_qrcode_refresh;
    private RecyclerView rv_select;
    //    private CountDownView mTimerMin;
    private CountDownTimer mCountDownTimer;
    private LinearLayout ll_qrcode_failed;
    private TextView tv_qrcode_failed, tv_count_down, tv_agree_vip, tv_vip_icon, purchase_notice_tip_tv, tv_close;
    private View purchaseNoticeContent;
    private ViewGroup purchasNoticeTip;

    private VipOrderAdapter mVipOrderAdapter;
    private List<VipMeals> mDatas = new ArrayList<>();

    private static final String PAY_SUBJECT_ID_KEY = "paySubjectId";
    private static final String TAG = "VipOrderDialogFragment";

    AnimationDrawable mAnimationDrawable;
    /**
     * 定时轮询二维码扫码结果
     */
    private static final int PERIOD = 2 * 1000;
    private static final int DELAY = 1 * 1000;
    private Disposable mDisposable;// 二维码生成后开启定时任务，支付成功，退出页面，重新请求都需要关闭定时任务。

    public static VipOrderDialogFragment newInstance() {
        VipOrderDialogFragment fragment = new VipOrderDialogFragment();
//        Bundle args = new Bundle();
//        args.putParcelable(PAY_SUBJECT_ID_KEY, playItem);
//        fragment.setArguments(args);
        fragment.setGravity(Gravity.CENTER);
        fragment.setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
        fragment.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        return fragment;
    }

    @Override
    public void initArgs() {
    }


    @Override
    public void initView(View view) {
        tv_vip_icon = view.findViewById(R.id.tv_vip_icon);
        purchasNoticeTip = view.findViewById(R.id.purchas_notice_tip);
        purchaseNoticeContent = view.findViewById(R.id.purchase_notice_context_tv);
        purchaseNoticeContent.setOnClickListener(this);
        iv_qrcode = view.findViewById(R.id.iv_qrcode);
//        iv_qrcode.setOnClickListener(this);
        tv_agree_vip = view.findViewById(R.id.tv_agree_vip);
        tv_count_down = view.findViewById(R.id.tv_count_down);
        ll_qrcode_failed = view.findViewById(R.id.ll_qrcode_failed);
        tv_agree_vip.setOnClickListener(this);
        ll_qrcode_failed.setOnClickListener(this);
        tv_qrcode_failed = view.findViewById(R.id.tv_qrcode_failed);
        iv_qrcode_refresh = view.findViewById(R.id.iv_qrcode_refresh);
        iv_qrcode_refresh.setImageResource(R.drawable.order_loading);
        mAnimationDrawable = (AnimationDrawable) iv_qrcode_refresh.getDrawable();
        rv_select = view.findViewById(R.id.rv_select);
        rv_select.setLayoutManager(new LinearLayoutManager(getActivity(), RecyclerView.HORIZONTAL, false));
        rv_select.addItemDecoration(new VipDividerItemDecoration(getActivity()));//添加分割线
        purchase_notice_tip_tv = view.findViewById(R.id.purchase_notice_tip_tv);
        tv_close = view.findViewById(R.id.cd_close);
        tv_close.setOnClickListener(this);

        TextView scrollLeft = view.findViewById(R.id.cd_left);
        TextView scrollRight = view.findViewById(R.id.cd_right);
        scrollLeft.setOnClickListener((v) -> ScrollingUtil.scrollListHorizontalByVoice(rv_select, -1));
        scrollRight.setOnClickListener((v) -> ScrollingUtil.scrollListHorizontalByVoice(rv_select, 1));

        if (mVipOrderAdapter == null) {
            mVipOrderAdapter = new VipOrderAdapter(getActivity(), mDatas);
            new RecyclerViewExposeUtil().setRecyclerItemExposeListener(rv_select, new RecyclerViewExposeUtil.OnItemExposeListener() {
                @Override
                public void onItemViewVisible(boolean visible, int position) {
                    if (!visible) return;
                    PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                    if (curPlayItem != null) {
                        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_VIP_ORDER_MEAL_ITEM, mDatas.get(position).getMealName(), ReportParameterManager.getInstance().getPage()
                                , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_VIP_ORDER, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
                    }
                }
            });
        }
        mVipOrderAdapter.setCheckListener(this);
        rv_select.setAdapter(mVipOrderAdapter);
        purchasNoticeTip.setOnClickListener(this);
//        mTimerMin = (CountDownView)view.findViewById(R.id.count_down);
//        mTimerMin.setOnTimeCompleteListener(new CountDownView.OnTimeCompleteListener() {
//            @Override
//            public void onTimeComplete() {
//                showQrCodeFailedUI("二维码已过期");
//            }
//        });

        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_VIP_ORDER_PURCHASE_NOTES, purchase_notice_tip_tv.getText().toString(), ReportParameterManager.getInstance().getPage()
                    , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_VIP_ORDER, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
//        rootLayout.removeAllViews();
//        View contentView = getContentView();
//        rootLayout.addView(contentView);
//        initView(rootLayout);
//        if (lastQRCodeInfo != null) {
////            if (mCountDownTimer != null) {
////                mCountDownTimer.cancel();
////                mCountDownTimer = null;
////            }
//            disPose();
//            showQrCodeContent(lastQRCodeInfo);
//        } else {
//            initData();
//        }
        super.onConfigurationChanged(newConfig);
    }

    @Override
    public void initData() {
        mAnimationDrawable.start();
        mPresenter.getData();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.dialog_fragment_order_vip;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected VipOrderPresenter createPresenter() {
        return new VipOrderPresenter(this);
    }

//    @Override
//    public String getPageId() {
//        return Constants.PAGE_ID_PAY_VIP;
//    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.ll_qrcode_failed) {//id == R.id.iv_qrcode ||
//            mTimerMin.setVisibility(View.GONE);
            tv_count_down.setVisibility(View.GONE);
            if (mVipOrderAdapter.getSelectedItem() != null) {
                //重新请求二维码，关闭上次的定时任务
                disPose();
                tv_qrcode_failed.setVisibility(View.GONE);
                mAnimationDrawable.start();
                mPresenter.getQrCode(mVipOrderAdapter.getSelectedItem().getMealId(), mVipOrderAdapter.getSelectedItem().getDiscountPrice());
            }
        } else if (id == R.id.tv_rmb_buy_notice) {
            //购买须知
//            String url = FlavorUtil.getHttp443Url(PayConst.BUY_NOTICE_URL);
//            Log.d(TAG, "tv_rmb_buy_notice url=" + url);
//            if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
//                toast(ResUtil.getString(com.kaolafm.kradio.lib.R.string.no_net_work_str));
//                return;
//            }
//            WebViewActivity.start(getActivity(), url);
        } else if (id == R.id.tv_agree_vip) {
            String url = PayConst.VIP_AGREEMENT_URL;
            String theme = "dark";
            if (SkinHelper.isDayMode()) {
                theme = "light";
            }
            url += "?theme=" + theme + "&bgColor=transparent&contentSize="
                    + (int) ResUtil.getDimension(R.dimen.m22)
                    + "&showTitle=1"
                    + "&marginL=0"
                    + "&unit=1"
                    + "&marginR=" + ResUtil.getDimen(R.dimen.m33)
                    + "&textIndent=0";

            ProtocolViplinkInter inter = ClazzImplUtil.getInter("ProtocolViplinkImpl");
            if (null != inter) {
                url = inter.getNewProtocolVipLink();
            }
            url = FlavorUtil.getHttp443Url(url);
            Log.d(TAG, "tv_agree_vip url=" + url);
            if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
                toast(ResUtil.getString(com.kaolafm.kradio.lib.R.string.no_net_work_str));
                return;
            }
            WebViewActivity.start(getActivity(), url, "云听VIP会员服务协议", "");
        } else if (id == R.id.purchas_notice_tip) {
            if (purchaseNoticeContent.getVisibility() != View.VISIBLE) {
                purchaseNoticeContent.setVisibility(View.VISIBLE);
            } else {
                purchaseNoticeContent.setVisibility(View.GONE);
            }
            PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            if (curPlayItem != null) {
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_VIP_ORDER_PURCHASE_NOTES, purchase_notice_tip_tv.getText().toString(), ReportParameterManager.getInstance().getPage()
                        , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_VIP_ORDER, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
            }
        } else if (id == R.id.purchase_notice_context_tv) {
            purchaseNoticeContent.setVisibility(View.GONE);
        } else if (id == R.id.cd_close) {
            dismiss();
        }
    }

    private void toast(String msg) {
        KRadioToastInter radioToastInter = ClazzImplUtil.getInter("KRadioToastImpl");
        if (radioToastInter != null) {
            UIThreadUtil.runUIThread(() ->
                    radioToastInter.showToast(getContext(), getResources().getString(com.kaolafm.kradio.lib.R.string.no_net_work_str), 1000));


        } else {
            ToastUtil.showOnly(getContext(), msg);
        }
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        //关闭购买弹窗时，关闭定时任务
        disPose();
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        super.onDismiss(dialog);
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Override
    public void showInfoContent(List<VipMeals> vipMealsList) {
        mDatas.addAll(vipMealsList);
        mVipOrderAdapter.notifyDataSetChanged();
        if (!ListUtil.isEmpty(vipMealsList)) {
            updateLabel(mVipOrderAdapter.getSelectedItem());
        }
        if (mVipOrderAdapter.getSelectedItem() != null && mVipOrderAdapter.getSelectedItem().getMealId() != null) {
            mPresenter.getQrCode(mVipOrderAdapter.getSelectedItem().getMealId(), mVipOrderAdapter.getSelectedItem().getDiscountPrice());
        }
    }

    @Override
    public void showInfoLoading() {

    }

    @Override
    public void hideInfoLoading() {

    }

    @Override
    public void showInfoError(String error, boolean clickToRetry) {

    }

    @Override
    public void hideInfoError() {

    }

    private QRCodeInfo lastQRCodeInfo;

    @Override
    public void showQrCodeContent(QRCodeInfo qrCodeInfo) {
//        Log.i(TAG, "showQrCodeContent qrCodeInfo="+qrCodeInfo.toString());
        lastQRCodeInfo = qrCodeInfo;
        showQrCodeSucessedUI(qrCodeInfo);
        disPose();
        mDisposable = Observable.interval(DELAY, PERIOD, TimeUnit.MILLISECONDS)
                .map((aLong -> aLong + 1))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> mPresenter.checkQrCodeStatus(qrCodeInfo.getQrCodeId()));
    }

    @Override
    public void showQrCodeLoading() {

    }

    @Override
    public void hideQrCodeLoading() {

    }

    @Override
    public void showQrCodeError(String error, boolean clickToRetry) {
        showQrCodeFailedUI(error);
    }

    @Override
    public void hideQrCodeError() {

    }

    @Override
    public void onItemChecked(int position, VipMeals vipMeals) {
        mPresenter.cancelRequestVipQrCodeDatas();
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        //因为不显示吐司
        if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
            toast(ResUtil.getString(com.kaolafm.kradio.lib.R.string.no_net_work_str));

        }

        tv_count_down.setVisibility(View.GONE);
        mPresenter.getQrCode(vipMeals.getMealId(), vipMeals.getDiscountPrice());

        if (vipMeals != null) {
            mPresenter.getQrCode(vipMeals.getMealId(), vipMeals.getDiscountPrice());
        }
        updateLabel(vipMeals);

        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_VIP_ORDER_MEAL_ITEM, vipMeals.getMealName(), ReportParameterManager.getInstance().getPage()
                    , ReportConstants.CONTROL_TYPE_SCREEN, ReportConstants.DIALOG_ID_VIP_ORDER, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
        }
    }

    private void updateLabel(VipMeals vipMeals) {
        if (vipMeals != null) {
            String vipLableDisplay = vipMeals.getVipLableDisplay();
            if (TextUtils.isEmpty(vipLableDisplay)) {
                tv_vip_icon.setVisibility(View.GONE);
            } else {
                tv_vip_icon.setText(vipLableDisplay);
                tv_vip_icon.setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    public void paySucessed(PurchaseSucess purchaseSucess) {
        if (purchaseSucess == null || purchaseSucess.getStatus() == null) {
            return;
        }
        if (purchaseSucess.getStatus() == 1) {
            Log.i(TAG, "PayManager.getInstance().onPayResult");
            //支付成功后，关闭定时任务
            disPose();
            PayManager.getInstance().onPayResult(new VipPayResult(purchaseSucess, mVipOrderAdapter.getSelectedItem().getVipDays()));
            dismissAllowingStateLoss();
            PayResDialog.show(getActivity(), true, "支付成功");
        }
    }

    private void disPose() {
        Log.i(TAG, "mDisposable=" + mDisposable);
        if (mDisposable != null) mDisposable.dispose();
    }

    @Override
    public void payFailed(String error) {
        if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
            toast(ResUtil.getString(com.kaolafm.kradio.lib.R.string.no_net_work_str));
        } else
            PayResDialog.show(getActivity(), false, error);
//        PurchaseSucess purchaseSucess = new PurchaseSucess();
//        purchaseSucess.setStatus(0);
//        PayManager.getInstance().onPayResult(new VipPayResult(purchaseSucess, mVipOrderAdapter.getSelectedItem().getVipDays()));
    }

    private void showQrCodeSucessedUI(QRCodeInfo qrCodeInfo) {
        mAnimationDrawable.stop();
        ImageLoader.getInstance().displayImage(getActivity(), qrCodeInfo.getQrCodeImg(), iv_qrcode);
        iv_qrcode.setVisibility(View.VISIBLE);
        ll_qrcode_failed.setVisibility(View.GONE);
//        mTimerMin.reStart(qrCodeInfo.getExpireTime()==null?-1:qrCodeInfo.getExpireTime());
//        mTimerMin.setVisibility(View.VISIBLE);
        tv_count_down.setVisibility(View.VISIBLE);
        if (mCountDownTimer == null) {
            mCountDownTimer = new CountDownTimer((qrCodeInfo.getExpireTime() == null ? 0 : qrCodeInfo.getExpireTime()) * 1000, 1000) {

                @Override
                public void onTick(long millisUntilFinished) {
                    tv_count_down.setText(CountDownUtils.FormatMiss(millisUntilFinished / 1000));
                }

                @Override
                public void onFinish() {
//                tv_count_down.setEnabled(true);
                    tv_count_down.setVisibility(View.GONE);

                    if(mCountDownTimer != null){
                        mCountDownTimer.cancel();
                        mCountDownTimer = null;
                    }

                    showQrCodeFailedUI("二维码已过期");
                }
            };
            mCountDownTimer.start();
        }
    }

    private void showQrCodeFailedUI(String error) {
        mAnimationDrawable.stop();
        ll_qrcode_failed.setVisibility(View.VISIBLE);
        tv_qrcode_failed.setText(error + ",请点击刷新重试");
        tv_qrcode_failed.setVisibility(View.VISIBLE);
        iv_qrcode.setVisibility(View.GONE);
//        mTimerMin.setVisibility(View.GONE);
        tv_count_down.setVisibility(View.GONE);
    }

    public boolean isReportFragment() {
        return true;
    }

    @Override
    protected void reportDialogExposureEvent(long duration) {
        super.reportDialogExposureEvent(duration);
        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        String contentId = null;
        if (curPlayItem != null) {
            contentId = curPlayItem.getAlbumId();
        }
        ReportHelper.getInstance().addEvent(new DialogExposureEvent(ReportConstants.DIALOG_ID_VIP_ORDER, ReportParameterManager.getInstance().getPage(), duration, contentId));
    }

    /**
     * 监听主题切换事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeChangeEvent(UserCenterInter.ThemeChangeEvent event) {
        Log.d("VipOrderDialogFragment", "收到主题变化事件: " + (event != null ? event.getTheme() : "null"));

        String theme = (event == null) ? "" : event.getTheme();
        if (TextUtils.isEmpty(theme)) {
            return;
        }

        // 处理isSameTheme事件：确认当前主题状态
        if (SkinHelper.IS_SAME_THEME.equals(theme)) {
            Log.d("VipOrderDialogFragment", "收到isSameTheme事件，延迟刷新适配器");
            refreshAdapterDelayed();
            return;
        }

        // 根据皮肤框架的主题事件更新UI
        if (SkinHelper.NIGHT_SKIN.equals(theme) || SkinHelper.DAY_SKIN.equals(theme)) {
            Log.d("VipOrderDialogFragment", "主题切换为: " + theme + "，延迟刷新适配器");
            refreshAdapterDelayed();
        }
    }

    /**
     * 延迟刷新适配器，确保在皮肤框架应用后执行
     */
    private void refreshAdapterDelayed() {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (mVipOrderAdapter != null) {
                mVipOrderAdapter.notifyDataSetChanged();
            }
        }, 300);
    }
}
